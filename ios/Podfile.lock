PODS:
  - app_links (0.0.2):
    - Flutter
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - Firebase/Messaging (10.25.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.25.0)
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 10.25.0)
    - Flutter
  - firebase_messaging (14.7.10):
    - Firebase/Messaging (= 10.25.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - integration_test (0.0.1):
    - Flutter
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Stripe (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.27.0)
    - StripeApplePay (~> 23.27.0)
    - StripeFinancialConnections (~> 23.27.0)
    - StripePayments (~> 23.27.0)
    - StripePaymentSheet (~> 23.27.0)
    - StripePaymentsUI (~> 23.27.0)
  - StripeApplePay (23.27.6):
    - StripeCore (= 23.27.6)
  - StripeCore (23.27.6)
  - StripeFinancialConnections (23.27.6):
    - StripeCore (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripePayments (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments/Stripe3DS2 (= 23.27.6)
  - StripePayments/Stripe3DS2 (23.27.6):
    - StripeCore (= 23.27.6)
  - StripePaymentSheet (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
  - StripePaymentsUI (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripeUICore (23.27.6):
    - StripeCore (= 23.27.6)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  firebase_core: a626d00494efa398e7c54f25f1454a64c8abf197
  firebase_messaging: 1541105e2a2a6ef8bd869bcc44157d31e82f3a50
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 88950ba9485052891ebe26f6c43a52bb62248952
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_native_splash: df59bb2e1421aa0282cb2e95618af4dcb0c56c29
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  integration_test: 252f60fa39af5e17c3aa9899d35d908a0721b573
  local_auth_darwin: fa4b06454df7df8e97c18d7ee55151c57e7af0de
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  Stripe: 9fec845645e39f371e6898926d096fd9c2feb5a5
  stripe_ios: 03c617acee72e48a2d055d096a4b0ed2afebb256
  StripeApplePay: 5f017e8dfe259fafbab70137776189deef754bb2
  StripeCore: 01ec57f0dddfe742054dc6a322f811426c25313d
  StripeFinancialConnections: 56698cb6274bf89fb8c76b934f6156f368e97765
  StripePayments: 6adf11faf1b7038e77aa97019410305c6adca79d
  StripePaymentSheet: 3eaf870c4388e44b0cc37e4c69d00b6957fd8bd7
  StripePaymentsUI: 59ccddeacad592b09fa67e8d641340820ddb4751
  StripeUICore: 879bbf5889265db13f52fac8aad7a176ba62481f
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 251cb053df7158f337c0712f2ab29f4e0fa474ce

COCOAPODS: 1.16.2
