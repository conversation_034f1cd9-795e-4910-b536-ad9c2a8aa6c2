<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Firebase.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore-FirebaseCore_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal-FirebaseCoreInternal_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations-FirebaseInstallations_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging-FirebaseMessaging_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-RunnerTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Stripe-StripeBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Stripe.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeApplePay.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeCore-StripeCoreBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeFinancialConnections-StripeFinancialConnectionsBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeFinancialConnections.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePaymentSheet-StripePaymentSheetBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePaymentSheet.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePayments-Stripe3DS2.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePayments-StripePaymentsBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePayments.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePaymentsUI-StripePaymentsUIBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripePaymentsUI.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeUICore-StripeUICoreBundle.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>StripeUICore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>app_links-app_links_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>app_links.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>firebase_core.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>firebase_messaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_local_notifications-flutter_local_notifications_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_local_notifications.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_native_splash-flutter_native_splash_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_native_splash.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage-flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>integration_test.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>local_auth_darwin-local_auth_darwin_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>local_auth_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb-nanopb_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation-path_provider_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple-permission_handler_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>share_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin-sqflite_darwin_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>stripe_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios-url_launcher_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
