${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh
${BUILT_PRODUCTS_DIR}/FirebaseCore/FirebaseCore.framework
${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework
${BUILT_PRODUCTS_DIR}/FirebaseInstallations/FirebaseInstallations.framework
${BUILT_PRODUCTS_DIR}/FirebaseMessaging/FirebaseMessaging.framework
${BUILT_PRODUCTS_DIR}/GoogleDataTransport/GoogleDataTransport.framework
${BUILT_PRODUCTS_DIR}/GoogleUtilities/GoogleUtilities.framework
${BUILT_PRODUCTS_DIR}/PromisesObjC/FBLPromises.framework
${BUILT_PRODUCTS_DIR}/Stripe/Stripe.framework
${BUILT_PRODUCTS_DIR}/StripeApplePay/StripeApplePay.framework
${BUILT_PRODUCTS_DIR}/StripeCore/StripeCore.framework
${BUILT_PRODUCTS_DIR}/StripeFinancialConnections/StripeFinancialConnections.framework
${BUILT_PRODUCTS_DIR}/StripePaymentSheet/StripePaymentSheet.framework
${BUILT_PRODUCTS_DIR}/StripePayments/StripePayments.framework
${BUILT_PRODUCTS_DIR}/StripePaymentsUI/StripePaymentsUI.framework
${BUILT_PRODUCTS_DIR}/StripeUICore/StripeUICore.framework
${BUILT_PRODUCTS_DIR}/app_links/app_links.framework
${BUILT_PRODUCTS_DIR}/flutter_local_notifications/flutter_local_notifications.framework
${BUILT_PRODUCTS_DIR}/flutter_native_splash/flutter_native_splash.framework
${BUILT_PRODUCTS_DIR}/flutter_secure_storage/flutter_secure_storage.framework
${BUILT_PRODUCTS_DIR}/integration_test/integration_test.framework
${BUILT_PRODUCTS_DIR}/local_auth_darwin/local_auth_darwin.framework
${BUILT_PRODUCTS_DIR}/nanopb/nanopb.framework
${BUILT_PRODUCTS_DIR}/path_provider_foundation/path_provider_foundation.framework
${BUILT_PRODUCTS_DIR}/share_plus/share_plus.framework
${BUILT_PRODUCTS_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework
${BUILT_PRODUCTS_DIR}/sqflite_darwin/sqflite_darwin.framework
${BUILT_PRODUCTS_DIR}/stripe_ios/stripe_ios.framework
${BUILT_PRODUCTS_DIR}/url_launcher_ios/url_launcher_ios.framework