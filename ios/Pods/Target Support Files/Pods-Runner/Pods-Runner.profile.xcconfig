ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/Stripe" "${PODS_CONFIGURATION_BUILD_DIR}/StripeApplePay" "${PODS_CONFIGURATION_BUILD_DIR}/StripeCore" "${PODS_CONFIGURATION_BUILD_DIR}/StripeFinancialConnections" "${PODS_CONFIGURATION_BUILD_DIR}/StripePaymentSheet" "${PODS_CONFIGURATION_BUILD_DIR}/StripePayments" "${PODS_CONFIGURATION_BUILD_DIR}/StripePaymentsUI" "${PODS_CONFIGURATION_BUILD_DIR}/StripeUICore" "${PODS_CONFIGURATION_BUILD_DIR}/app_links" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_core" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_messaging" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_native_splash" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "${PODS_CONFIGURATION_BUILD_DIR}/integration_test" "${PODS_CONFIGURATION_BUILD_DIR}/local_auth_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/stripe_ios" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Stripe/Stripe.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripeApplePay/StripeApplePay.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripeCore/StripeCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripeFinancialConnections/StripeFinancialConnections.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripePaymentSheet/StripePaymentSheet.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripePayments/StripePayments.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripePaymentsUI/StripePaymentsUI.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/StripeUICore/StripeUICore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/app_links/app_links.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_core/firebase_core.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_messaging/firebase_messaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications/flutter_local_notifications.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_native_splash/flutter_native_splash.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/integration_test/integration_test.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/local_auth_darwin/local_auth_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus/share_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin/sqflite_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/stripe_ios/stripe_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/Firebase" $(inherited) ${PODS_ROOT}/Firebase/CoreOnly/Sources
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -ObjC -l"sqlite3" -l"z" -framework "Contacts" -framework "CoreLocation" -framework "CoreTelephony" -framework "FBLPromises" -framework "FirebaseCore" -framework "FirebaseCoreInternal" -framework "FirebaseInstallations" -framework "FirebaseMessaging" -framework "Foundation" -framework "GoogleDataTransport" -framework "GoogleUtilities" -framework "PassKit" -framework "Security" -framework "Stripe" -framework "StripeApplePay" -framework "StripeCore" -framework "StripeFinancialConnections" -framework "StripePaymentSheet" -framework "StripePayments" -framework "StripePaymentsUI" -framework "StripeUICore" -framework "SystemConfiguration" -framework "UIKit" -framework "WebKit" -framework "app_links" -framework "firebase_core" -framework "firebase_messaging" -framework "flutter_local_notifications" -framework "flutter_native_splash" -framework "flutter_secure_storage" -framework "integration_test" -framework "local_auth_darwin" -framework "nanopb" -framework "path_provider_foundation" -framework "permission_handler_apple" -framework "share_plus" -framework "shared_preferences_foundation" -framework "sqflite_darwin" -framework "stripe_ios" -framework "url_launcher_ios" -weak_framework "LinkPresentation" -weak_framework "SwiftUI" -weak_framework "UserNotifications"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/Firebase" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/Flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/Stripe" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripeApplePay" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripeCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripeFinancialConnections" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripePaymentSheet" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripePayments" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripePaymentsUI" "-F${PODS_CONFIGURATION_BUILD_DIR}/StripeUICore" "-F${PODS_CONFIGURATION_BUILD_DIR}/app_links" "-F${PODS_CONFIGURATION_BUILD_DIR}/firebase_core" "-F${PODS_CONFIGURATION_BUILD_DIR}/firebase_messaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_native_splash" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "-F${PODS_CONFIGURATION_BUILD_DIR}/integration_test" "-F${PODS_CONFIGURATION_BUILD_DIR}/local_auth_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "-F${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/stripe_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
