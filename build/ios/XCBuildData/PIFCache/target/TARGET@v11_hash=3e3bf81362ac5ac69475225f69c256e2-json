{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c682df2c7e135cec4234aeea49d0125", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988fad68f91e462a8067482623b58d732c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981702f546778fc3c139975a416799aec6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a207145098dd0dbc33f3b7f533c952e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981702f546778fc3c139975a416799aec6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5ded858f19ec5adad22d91aa2bfea21", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871bd4dcc1e106ade71bf3aaae32a0ffc", "guid": "bfdfe7dc352907fc980b868725387e98b81a67f567dbf592bab278336f6d6131"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893deb226039df5ce9b9b794fec015a20", "guid": "bfdfe7dc352907fc980b868725387e9831469b36f6ab70174f0246624fb78841", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98552c5cf46cdba9940f4d9171b4cb29b3", "guid": "bfdfe7dc352907fc980b868725387e98c2cd8ba87c2a5a524dfbf4efa3b0d192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980580cc69a26fb4a98d71b6d55bfae613", "guid": "bfdfe7dc352907fc980b868725387e983fb1b76369cb83342dfb65a4ce388b2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829776695a43533b76ad502a0f28d5880", "guid": "bfdfe7dc352907fc980b868725387e9840f7a5140e37161761f693d9bfa414e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4eb1bc3020fd186dbcd16a80c7621d4", "guid": "bfdfe7dc352907fc980b868725387e98a0e1caa194657c59b32da8136165cd3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c8e8873b80613c74ef75c16db831307", "guid": "bfdfe7dc352907fc980b868725387e98b610dd866967b6d535c3d2e85fe16d03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806a8cf6def0fba18ad0e71b37cac744", "guid": "bfdfe7dc352907fc980b868725387e9824f5ee7acd3505f2819570575987ba0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b368fa1c16853aacf8aa984dbf65eff9", "guid": "bfdfe7dc352907fc980b868725387e9875b30da98cbaadd33d539d792049ce87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdfce38a75a37036c9d526fda60ad362", "guid": "bfdfe7dc352907fc980b868725387e98000bb2e243d75d3f267aac018db1da73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9c23c9d5dd3997b0d2a0338e9d3382", "guid": "bfdfe7dc352907fc980b868725387e9829afc15dd0948480315d39e22c438815"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ac728a8e42e724da8381b8d737e6b1", "guid": "bfdfe7dc352907fc980b868725387e98a3fb57e96db6b24ccb9533b504de4a4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714ff1b0d1ee600609bc680debc2feba", "guid": "bfdfe7dc352907fc980b868725387e980f9def37a92f37e445ffeb4f15fb545c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e87bd0fc5e28bae7c8353f4359175405", "guid": "bfdfe7dc352907fc980b868725387e98e520b0d5e17635661ba79efb8e3cda01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd9ebb53b2b661befdaf062ce76aa753", "guid": "bfdfe7dc352907fc980b868725387e982e1ff090c7781abb3d909cc0203abce2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880eb6d5f9aa9feba2fc175d636a5e9ae", "guid": "bfdfe7dc352907fc980b868725387e984f892eb1f8234466d785d16caa6a29a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a27ea263f12c6d033bb391819301c76", "guid": "bfdfe7dc352907fc980b868725387e983ae956ca5c24d7755f9da35c75360d18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbada4a7dc9aeac80cece15d16226672", "guid": "bfdfe7dc352907fc980b868725387e9889e628332605536b05c7756c31772370"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf209fc0e5dcc74b96429be7d0664c52", "guid": "bfdfe7dc352907fc980b868725387e9872dbd26bad57d05f65cf60c112f27588", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863fc271a04e46dcb536c81e27825db36", "guid": "bfdfe7dc352907fc980b868725387e981ad7b7f8dbf5d27ff29cabe9de1afbf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4a4f5f111207449d298ae73a0ef6d5d", "guid": "bfdfe7dc352907fc980b868725387e98251b8819d23dea8324b9b992a9a747d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d203c955d824e4149de1b599a34e669f", "guid": "bfdfe7dc352907fc980b868725387e986c40b9ab827460000ef1202fe8183986", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98feb2ca6f8e6024fd4fb7e77090599286", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c1221b8b47da0fb003a9bcf87fe0fe83", "guid": "bfdfe7dc352907fc980b868725387e98223a288ae34d12005592082e2ebf5b2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b81cfc1c435ffa55aab043e0c364df", "guid": "bfdfe7dc352907fc980b868725387e98a958dcfca54a99b331f1c00f0da2e895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985762ca2ec1494250afacdef7f1011cfe", "guid": "bfdfe7dc352907fc980b868725387e98f52f72e2ed5fe9c85bea8bb483bddfd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0e4d843ce0905dfdb87fbceecb20bb", "guid": "bfdfe7dc352907fc980b868725387e982e9321a14d5d9b5b7222cc6d34672d71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d30a3ea13af2f9b35b7055da9da695d7", "guid": "bfdfe7dc352907fc980b868725387e9808d04952a536dfa3045d16b75c60261b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b2c1376090b9a8052ec5eeae80f6f8", "guid": "bfdfe7dc352907fc980b868725387e989082c8d6e182a74b5d8652e0ed81bbb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c59e5c1b9b9d74739217cb7c76d1bfe", "guid": "bfdfe7dc352907fc980b868725387e98174e78c2d4ba2349e08326e0dd8d3820"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863577f89dba2a600c2ba350f3e80167b", "guid": "bfdfe7dc352907fc980b868725387e982c44bea10b7f462d3ca4ead801a4a9d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a748d936947e0a76e5b307d146f0e2ed", "guid": "bfdfe7dc352907fc980b868725387e989e5e3efa2b561add43085bbc3c43dc72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61ff4b5a7aaf4f92cc7432d3eefc40d", "guid": "bfdfe7dc352907fc980b868725387e987848441a884dc08b701dc630bd7bc31d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980acbf4b323232ee34d017b8d38c043fa", "guid": "bfdfe7dc352907fc980b868725387e98c013c844cf2a5f1f477498b011f423e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae904dc2600866b28ac3e3b0870d8c5c", "guid": "bfdfe7dc352907fc980b868725387e983a516b61465a2a4fc8c64563d275007f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa40e5c0c9ee72b166551f114509ccd8", "guid": "bfdfe7dc352907fc980b868725387e985cf05c4c1d405320556419a0a445c68b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b68cb7e014af64f6c920cdb4acf6db", "guid": "bfdfe7dc352907fc980b868725387e98c2f84e6b9b698e5de2313a581e28517e"}], "guid": "bfdfe7dc352907fc980b868725387e98b87a5fa25b09d8131c02e3f9f550b8e1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e9818bcc495545e60901af0fbaa4858be47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854f47e9fb3c328111c0aab27bf0ad66b", "guid": "bfdfe7dc352907fc980b868725387e98cf90bb9a041a0fd080a3c635051a4017"}], "guid": "bfdfe7dc352907fc980b868725387e98dca4500b8ff38d3a21e03e99fccaa4c3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9826adcc5252d967cce0720252707ab019", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9872b7b7ae0a193ba6bfee24397a25fde7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}