{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a5d071737b6c28e50e3aec984be6f512", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9831bfa5e9dc7fdbdf4526195d421d180e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d97066dab9f1f16a3fad7ea514def517", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f47f69c16de3def24cd32a40cde45371", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d97066dab9f1f16a3fad7ea514def517", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e12e9cdafb138ab64b589b122b6b6d30", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98efed6c991af1cf80203a6fd1d7ff0e45", "guid": "bfdfe7dc352907fc980b868725387e98c3964b18c5b7d5c47323431dfa58cc09", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9833cfe8ba5cb31851170eae67c0669c82", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9813daba5ce8f6c319654052ef0e826f42", "guid": "bfdfe7dc352907fc980b868725387e9806dffbddadd2939b5dec55c6a2087d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f56023021915cab1af1cd78f654d4c4", "guid": "bfdfe7dc352907fc980b868725387e98ed613e22d2b22870be134953f3a32fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860c1e2274cb3e80527a5a2adc401e967", "guid": "bfdfe7dc352907fc980b868725387e98f9f368fdc336a387d4c0149e911b5f91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8851b1af4139438b2d2c446c30b80af", "guid": "bfdfe7dc352907fc980b868725387e981dc653598bcef7c234d5e14692d04aa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73f3bc7fa22f2da7e4fff6fdeaaf6c7", "guid": "bfdfe7dc352907fc980b868725387e98cc05e8a8689d21b41340e22764f79d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db98c129afdb8185b53ec28b0a033f4a", "guid": "bfdfe7dc352907fc980b868725387e985415207838bbc171c313129d728b7d3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051c2b947d46f4ec8dca9f46c6e8993d", "guid": "bfdfe7dc352907fc980b868725387e9826f3f0b006230a5fae761eda9ea643f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98832f7c2292339132cbf8cc63037c9720", "guid": "bfdfe7dc352907fc980b868725387e98e388c3177af8e6a1b4f26f2d07389733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3ee81fb38ef4981af84945a95ad4e5", "guid": "bfdfe7dc352907fc980b868725387e9800ba34f37862fce6c9919f850cc82e41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e539777c2590c9a94d47ca1c53f183a", "guid": "bfdfe7dc352907fc980b868725387e98e0ea225eb24503979ef91a90a4312a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90d1bae2757a19b2587896009c521eb", "guid": "bfdfe7dc352907fc980b868725387e983bb19d781674011bc4c946cf7a16ec13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3a9790ed0bf0e58e7cdb31a205921f0", "guid": "bfdfe7dc352907fc980b868725387e982856fdf22e1079c1ae8c5178444f789f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898115771a959bbc1fa613b05fcd7eddc", "guid": "bfdfe7dc352907fc980b868725387e98a4e953bd342fd5a9b0f28f852ea60468"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e17cea38eb697e365ee6462d97a16f", "guid": "bfdfe7dc352907fc980b868725387e98813584ef86baec3e8d5d6da58b99792d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98438edeaa018021adb98d33ef0065bdf1", "guid": "bfdfe7dc352907fc980b868725387e9818d9844b380db396e8a9b06d462691a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980144fe3d8b2371dfb2a8dcbc353aedfb", "guid": "bfdfe7dc352907fc980b868725387e98b29dc3a5d51ea3b6022999ebee35e47e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839cc487daf55cfd762deeeb6df8cd18b", "guid": "bfdfe7dc352907fc980b868725387e980db15687e159f209e5e64dfda80b9100"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2c02beabcf017f371b11406a93bc74b", "guid": "bfdfe7dc352907fc980b868725387e98ad82dea968bba862b54402f310dc7d0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221b0376b09dd0c80dbd734fa8dd3100", "guid": "bfdfe7dc352907fc980b868725387e98497db4fbb8766d71b65defbccc406220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf788c92e9aec253e738ab8e9d1bc7f", "guid": "bfdfe7dc352907fc980b868725387e9888acf176a9b1f33856c10604b18bccf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15996712e777c94767f66cc53a5dbfe", "guid": "bfdfe7dc352907fc980b868725387e981e34f15b8db8f7a7ab21090c9f45d477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a0d1fc98dec4e8d494b0f7a71f2bc7", "guid": "bfdfe7dc352907fc980b868725387e98dba7f563d2aa1ac3d59664d87cf0dcc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980138ba60aafc629432990adf01785c81", "guid": "bfdfe7dc352907fc980b868725387e9882300b84fd84803eae4afbf9244d1da4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879767465a871d4675980558e02f59409", "guid": "bfdfe7dc352907fc980b868725387e98be69f2542100ae82b0ea22919b12a7f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703491d4cc9ba71220a4ed30b09dbdcd", "guid": "bfdfe7dc352907fc980b868725387e98f9868dd86fa6a04da2a2f6253bed8a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb3fd0d4facdb8c96acdf13cb98e890", "guid": "bfdfe7dc352907fc980b868725387e988037d7771080b1bd5ec5c13c885b8335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f8ea4239eaf0fa97afb0550e1e7a2f", "guid": "bfdfe7dc352907fc980b868725387e981160ccb9458c59fea387e91d07b9e05c"}], "guid": "bfdfe7dc352907fc980b868725387e984bd0d7c9c2f2b4d0493af3d993eb648c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1eac3de57722ace0823915d832042af", "guid": "bfdfe7dc352907fc980b868725387e981d805bb4c7f5e705ffb2a94aa8afe48c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e0ada8045037234837decfaf45324f", "guid": "bfdfe7dc352907fc980b868725387e98e91666d59f70352f90b1ccd19ef4ae70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e984441befb919538d7c47ece2390112199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802ff45adc8d810634cbedcff9433b230", "guid": "bfdfe7dc352907fc980b868725387e9896662252494451ec1b039494e4c7403e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e52b751c087231757083d511f5ac87", "guid": "bfdfe7dc352907fc980b868725387e9879c849844b358a1e20c52980efa3ea03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8e79f5d6d7f995cac00a2068e14563", "guid": "bfdfe7dc352907fc980b868725387e987f9083e26e5f0bc40f78a89f7b7054cd"}], "guid": "bfdfe7dc352907fc980b868725387e985820726cce6158df2375bf0ee7cc8249", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a7b956605fd31b580986c49f967e891b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}