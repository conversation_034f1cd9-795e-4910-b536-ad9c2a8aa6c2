{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870b555b8097b2cae34315e65f60b4ae7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985080c507061b08f5d01e88895665fe79", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98533e0861c841fa0bf3ff3f7189311b97", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d0a11b8cba16ec1274385bb65a9e3753", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98533e0861c841fa0bf3ff3f7189311b97", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982ab23ceb1329aa4db70632054bba3a5a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e569047b6e86d63b3673ff514160831e", "guid": "bfdfe7dc352907fc980b868725387e985396d8d4e012a17aaa3089a565b04ace", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f196f3dc887597adf8350fa9f1972130", "guid": "bfdfe7dc352907fc980b868725387e9846d1759aa587da26a54ff7c087a2f03c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb20595b22ddcc3015c5c20b0b041476", "guid": "bfdfe7dc352907fc980b868725387e980b0b1e31df66aa9fd6b15a6c7a6ba1fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853442924c9f8c6644f5a789ae13b96c7", "guid": "bfdfe7dc352907fc980b868725387e9831a3460bd17006611aa4c058a962ca67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c10f98f0afc11671a83e38e6164bdde8", "guid": "bfdfe7dc352907fc980b868725387e98bf7eefb9b2b8282389562485e8d21de7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a39804e77499ffe27d27e0af2beb7fb5", "guid": "bfdfe7dc352907fc980b868725387e984a0b9102b662b70352df8fbd69a6122b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e96282c4d78afb80b71ae293d2240b", "guid": "bfdfe7dc352907fc980b868725387e98474b2adedd6a2963cbd7266b1f2b0772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0cf4b9d189c5faf1194b57530cef803", "guid": "bfdfe7dc352907fc980b868725387e9899e37fd97cd85d76127cabb523b3bee9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c6a947642861de7aee22fb6fc83d04", "guid": "bfdfe7dc352907fc980b868725387e980b6efffcb9e697bfe2fb5d7e93342e48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a18d2bf942ef283d13d1e34e17fa883f", "guid": "bfdfe7dc352907fc980b868725387e988f61d03327385908fe8440bba6af674d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc8db3c53b530d232e5ee70d21e4748", "guid": "bfdfe7dc352907fc980b868725387e9874bbc833f1de75eeb6ef670981bcfeb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f834a93734c524c40e57349b767965f", "guid": "bfdfe7dc352907fc980b868725387e983606e30136b1a93209c70d1c2c719da0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986001398a42af73ec4d047aff4214967a", "guid": "bfdfe7dc352907fc980b868725387e983fa05f753ff1d2cebe6e555e27691a23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e0b1dd42f1604d1bbab187a56d95e64", "guid": "bfdfe7dc352907fc980b868725387e9846fc53a20aab7df209230806fb7286fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ece039a58c09344d14a70a9499f5de", "guid": "bfdfe7dc352907fc980b868725387e9810f8714f9504a10e0901a7f848f7eb5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085a5b8110e41fa23785e141773b7322", "guid": "bfdfe7dc352907fc980b868725387e98706dccca0a3d44759f86b9cfff039176", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea6d0ac2117577524096c123ad65df33", "guid": "bfdfe7dc352907fc980b868725387e98cf9cb8ba2829f2be25e7e2eee31d82bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806edc05079b9a043a546120912aaaa62", "guid": "bfdfe7dc352907fc980b868725387e98b09cdaf0e79c68737716db512834662b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f75fbb3bec424f8cad7f01fc7b34af9", "guid": "bfdfe7dc352907fc980b868725387e9876190e5655b35ea2b66e4e673172d885", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdce7625c0f708f79f00fd6d8d8af758", "guid": "bfdfe7dc352907fc980b868725387e98c660cf36d1597e8213c927b6ffb34b25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118cfb6f074080138b3fe31c8f77c34b", "guid": "bfdfe7dc352907fc980b868725387e9815e2fcf7f9490fa93fd1d46e2826dc27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839142e2bfa2b0bb180b6ae92cf0146d5", "guid": "bfdfe7dc352907fc980b868725387e989cceb9e5a285aadefa25cd69388231f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701ae1a45d530d72dd438eea08612a8b", "guid": "bfdfe7dc352907fc980b868725387e987febb8685f6ab90010aa5af91d3ea3ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15ffa6122718294ad17800c2a4f45ef", "guid": "bfdfe7dc352907fc980b868725387e980e32220a5c5a54667f3636813e1f7a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043c550eb424e67f3102eb834bfb2199", "guid": "bfdfe7dc352907fc980b868725387e98502d8c962e12e0c0222d455be1d60b23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98784ca3540a45427533da8de4c480202c", "guid": "bfdfe7dc352907fc980b868725387e98d998ece1f004e40fd9f11d11bd523f19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ae1a4537f519e70d06f2c27632abd19", "guid": "bfdfe7dc352907fc980b868725387e9883975ba0be58a29984fd28a953656813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d750b7c3053b4e1a8abcf6d7146fc6a", "guid": "bfdfe7dc352907fc980b868725387e9848fc64e9fe4fa641a75e50aef11030fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860a8c73f59c529a40d33cf53890af33c", "guid": "bfdfe7dc352907fc980b868725387e9811494eb094af079cfbd836a4ac1ac8c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e09c714ef2ad5132c764987281e88ad", "guid": "bfdfe7dc352907fc980b868725387e985238d944d36a7e107408bd8b09b4ba57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdee85e97c0ee0b0d682c77f27239a93", "guid": "bfdfe7dc352907fc980b868725387e98b028d9a59b8bef807aa9c84701ed18cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b78e2925ac99a32fdb6d20f197f606e2", "guid": "bfdfe7dc352907fc980b868725387e98e3cec6aabab82d33f12f8caa627b7106", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984a667c84d2b0ebcc233688d4937a8682", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dc72fd8289732ea1a05d94f865fbd0f3", "guid": "bfdfe7dc352907fc980b868725387e9830be50658f380bbcca030754b6148626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9eae2eb0eee04a2a768da836f1533b", "guid": "bfdfe7dc352907fc980b868725387e98ae33edec04800890f4084195eb1ba026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ffc231567cb97f8645838ecf2b2ef6", "guid": "bfdfe7dc352907fc980b868725387e989faedfe47b5cbdb143d5fc2a291da255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d6c1b525e96c3cb32024daf064de47f", "guid": "bfdfe7dc352907fc980b868725387e98cb32d19d4b3b0cf2e82bc2146aa083d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0d45cfbe5176537c329c4a59a14c4a", "guid": "bfdfe7dc352907fc980b868725387e98bdf6bb5b3ea950a25cc0fbaee212f71d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2aa276fc7d0d601bb80b432273662a", "guid": "bfdfe7dc352907fc980b868725387e986f08b9abd812350e6d0a2170200794c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871156b7ef09065dcc375e7a877e9440c", "guid": "bfdfe7dc352907fc980b868725387e9819ea49625284c89ffaf40325f2fd8df4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e8bec1154c165c70116ff0675ae99dc", "guid": "bfdfe7dc352907fc980b868725387e98bd7e4906f768086395cb11a30755bba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e78b2c608c610d3bc1a1d567dfd68da0", "guid": "bfdfe7dc352907fc980b868725387e9823b4bc0d40fbd39c392177ee96095d3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff71d94e6fc7c16b9dc6f38f6af4b9db", "guid": "bfdfe7dc352907fc980b868725387e98b8152e00052f2d4aaee7514a6b1eb363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0ccab1255946ac0ec4916d317a1b4b", "guid": "bfdfe7dc352907fc980b868725387e9843af0f08229548edd20dd51919ae75f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da441cae789a4a52f797f4e716a473b4", "guid": "bfdfe7dc352907fc980b868725387e9803e0cc5b42fe037f2e4fda80137f3d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b54a75f9ac67ed591ed96f6a671479dd", "guid": "bfdfe7dc352907fc980b868725387e98f76f24ca8a10dc4b179fdee0d42fad90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825cd9cfa3f52d9d69ebb30f0f8812870", "guid": "bfdfe7dc352907fc980b868725387e983c74f2f67805286eb596fc86a7588fd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e936930c3b21d876927f8db73c5eac1f", "guid": "bfdfe7dc352907fc980b868725387e98cdb38681260ac1a3138ef707aea0340a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cc9740c1f43d2917fb2d71673ede7e0", "guid": "bfdfe7dc352907fc980b868725387e980cf2fefa499d33d8e2af09f9cdf8ca19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df4d5b09e55801d8be16a52f4bfcc450", "guid": "bfdfe7dc352907fc980b868725387e9853559cb8c943d48abdd06560cbb6f2db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a3530e5efa718186c93a8fc2a0bad8", "guid": "bfdfe7dc352907fc980b868725387e981d1b93ce513b287e64b9e58099c9c8f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881edb869c30d4c0e9aeb222ea2fad4e7", "guid": "bfdfe7dc352907fc980b868725387e9898788cbd5baaaf53a842a867c8ec5367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8db4f2a15e4d7185f767b001ac2eb5f", "guid": "bfdfe7dc352907fc980b868725387e98dc6291a36835c8ad064e217abd2b73d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c04d9c47106d115224f12d11e1d68b", "guid": "bfdfe7dc352907fc980b868725387e98a6cd6fac69301411a9c04b3beccfe7eb"}], "guid": "bfdfe7dc352907fc980b868725387e986ebbc7d63eb105c3f8e407d7de196d38", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e98e6830d4b897022ee6e78308145ace9a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e52b751c087231757083d511f5ac87", "guid": "bfdfe7dc352907fc980b868725387e9803728182da2fbfebbe85a6034b401e2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865700059f48bf1e775cb5da2bca8c8b5", "guid": "bfdfe7dc352907fc980b868725387e98dbcec9ce22aab28f9eb2dc2cb7a458cb"}], "guid": "bfdfe7dc352907fc980b868725387e98195878ad3eabb8f7c5ebfd0f6061223a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d40917b604bbd578ea14bc7e89e1af5f", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98bf73296ebc629f7f18e68e2c49043537", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}