{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98902c2399cd04c341473f98c0a04d9389", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f62ccd385bd44477132dc7fe6b20182a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985dc1a77bcf9952950c43ce69bb78da00", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffc61380e711a6f50e6a4693aa7ccccb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985dc1a77bcf9952950c43ce69bb78da00", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9874d0507fe886a3ab8fbc9725a0faa90f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871266f47c08e253d2ae4fed6ae5b036a", "guid": "bfdfe7dc352907fc980b868725387e9872c0f951cfce1084d73d5766d221dad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b7eb2d5abe35d3d2817c8b63381644", "guid": "bfdfe7dc352907fc980b868725387e989c438300a54261653e2871436561925b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ed86cb79f9b6b24ee6d102f68cc9a1", "guid": "bfdfe7dc352907fc980b868725387e98cc5a909cf485113d682420c9bffa8aba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d406acd81d3b678c620b5cf27d4479e8", "guid": "bfdfe7dc352907fc980b868725387e98a9f6da464bfd9f0b6e34a17dd3bbc5e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd803eef41cf86b40888d6e4c05bef8", "guid": "bfdfe7dc352907fc980b868725387e981d846f75f251c2b4c55c0bda222a8879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ab5462469f1743d807c5b067800638", "guid": "bfdfe7dc352907fc980b868725387e98723b7302571dd617433819722f35b857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ce0b571a24ef8d7b8b7d0163567295", "guid": "bfdfe7dc352907fc980b868725387e98a748e6f6fa8815c7de6081137d8fda92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360efe14d0d83d34beb352e2d3607bcd", "guid": "bfdfe7dc352907fc980b868725387e98fe6670dd44f5b9302669c7574bcb7c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b999002b19214a98cb650b85a6827d4", "guid": "bfdfe7dc352907fc980b868725387e9818758e88a559e8b43f140257a15c0b4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989729cf29ee491e72c03efec3c5b1fa72", "guid": "bfdfe7dc352907fc980b868725387e98eb99e5219336cda3e08cb1959a57ff87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c257c0fd16410f67e02a00325d5f4e4", "guid": "bfdfe7dc352907fc980b868725387e981ed3666f3cf60f52bb584ca8d19fc4c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de50132cc39895c5e96305bf74e2e4a", "guid": "bfdfe7dc352907fc980b868725387e986d38452ad2e51a0878cdaf2e5fce93db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c86e6c066204b2f6df5c67ef0e7a8f", "guid": "bfdfe7dc352907fc980b868725387e98098584fce918dd714b1270679ea7c6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b091d695a2ab23aeb1a2f3aa35c85c", "guid": "bfdfe7dc352907fc980b868725387e98e7e683c1508db5d69ead4f39d64b0ab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459b5c57529568fa27a6c5719459afbe", "guid": "bfdfe7dc352907fc980b868725387e98818e4308eccb8d07d835a3f4d19ffe62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a2b687e543f920517dca14957ff34c", "guid": "bfdfe7dc352907fc980b868725387e98a0aea0dfd3e27e9d9f688bd5024927ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4a329a36a7b19249b308529977fb9f8", "guid": "bfdfe7dc352907fc980b868725387e98437df3b2c4569b3338407adaecccca9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20820d8edbc621f4722376c94745c57", "guid": "bfdfe7dc352907fc980b868725387e982a0440fb5b1ffc6ac2503192f59c71ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d217a007de8a698f4150eccecee62c1e", "guid": "bfdfe7dc352907fc980b868725387e9880a328724cec9b2e93ea758b5fd70d01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98003a6b73d36566d06cb7dd746ae9c5af", "guid": "bfdfe7dc352907fc980b868725387e98a93e1bb733a7c554bc206f99f58f3d8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b393bf2df5b0b509c37134ec371850e", "guid": "bfdfe7dc352907fc980b868725387e98af1b62f41c9181a3c3538799698da9b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811af2c811bdfcca3acdda422f9210cd3", "guid": "bfdfe7dc352907fc980b868725387e9854fc5a0bd95da94e160d09ac05516123"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98564cf8d229d4f29e1e811a9cb4c0a77c", "guid": "bfdfe7dc352907fc980b868725387e98e57fcb6287621108724149bb710c39ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df537d9c04e8406b9e0c56e786e7cdd6", "guid": "bfdfe7dc352907fc980b868725387e98800b5550b52a13ca161eca61747b68c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836484f7a3e035b6216a7475b26ac5d34", "guid": "bfdfe7dc352907fc980b868725387e9849c1bf38c14e15a0608abc11d6f480c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba81c73224c8c4ba445a75cfb9b33e50", "guid": "bfdfe7dc352907fc980b868725387e98fbb0bbbe0ac68cbd7bc08764d177a3c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987852798ac1da554453a15bfef4bf053d", "guid": "bfdfe7dc352907fc980b868725387e98b659c2ac906b91866bc77ee03f42485b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98174cb7d1a4ed2a76b8397e39abbedcca", "guid": "bfdfe7dc352907fc980b868725387e98c9e66ec3bae207659ca3a343bd42e2b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ebfdf36dbd9cbb190d194804ce75c51", "guid": "bfdfe7dc352907fc980b868725387e98c2f88502daa103e4498a3475e5ffa7ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854b798728914c21fde49637849e2be0c", "guid": "bfdfe7dc352907fc980b868725387e98c76e498de55080dc0b02d42f667d0139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1a4ba2896da008fcabbb98cb2e6e8aa", "guid": "bfdfe7dc352907fc980b868725387e98410050fca93961a6c5666474b69379cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf7a2e43b052399629cad457513bc463", "guid": "bfdfe7dc352907fc980b868725387e98ef49ff24b37c5c87115f2dfd9966b406", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c74ed146fe4b15895f304709f6ae851", "guid": "bfdfe7dc352907fc980b868725387e98fcda42430fd6127b5b6039b2f54d78e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb95bd1561fad919c3b5539b2c547f78", "guid": "bfdfe7dc352907fc980b868725387e98a02d30f639ba222eae290d9b8ee1f411"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed4afd1bed79d62d20e07eb54333bd4", "guid": "bfdfe7dc352907fc980b868725387e987c72050a3aad22353322b77fe14ebf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983782b437ea41d3af101a01a940220b5a", "guid": "bfdfe7dc352907fc980b868725387e987214958850594b61df808a121624b1f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc33f243968b46727bb875b67c1d5071", "guid": "bfdfe7dc352907fc980b868725387e9847eb89257b5aaa6ecb995e39ab8387c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981204de50c866f3774f61458e779a31f2", "guid": "bfdfe7dc352907fc980b868725387e98e064818f56fc4a51635d5e9511349c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cd85f6aeeddb6867f354329d9cec1e7", "guid": "bfdfe7dc352907fc980b868725387e98d8877e937a4e7bc35b9a10287effab04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c64611ed38a098312a8cf6577395a190", "guid": "bfdfe7dc352907fc980b868725387e98612e675ddf4aeddded4d0719e081335b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d41f1016ac1f7f560194f3df1023cf5", "guid": "bfdfe7dc352907fc980b868725387e98459009a9c34c0ef4f07ec60c5e980c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bbd64a30612b1e85d03976777572687", "guid": "bfdfe7dc352907fc980b868725387e98aed920581883e1c13f6b4f6f59e5ece1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985324ab93c1515ce4dbeec994b6b14d0a", "guid": "bfdfe7dc352907fc980b868725387e98da8f7867239192f2b80ce7805bf13321"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823be06e6a4b6648ed0cb64526cf61dd2", "guid": "bfdfe7dc352907fc980b868725387e987b53f67259ab2dd1f5adcc3c78087a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988689cdf77a19082176b3ed3925e6756a", "guid": "bfdfe7dc352907fc980b868725387e98256f2ac4be4dd2e4a71c25361372467d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb9023f09323e457dda1e64e45ddcca", "guid": "bfdfe7dc352907fc980b868725387e9841b0ef9b4cb8a1be02acf80adf64848c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff1a7fba4b2000a484a6a82f4cad9971", "guid": "bfdfe7dc352907fc980b868725387e98e33d7235f37f1708539bb8988bad73da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817715e12ebbe5b6146041dde24be104e", "guid": "bfdfe7dc352907fc980b868725387e982327faaa2095671d1ddea4cf65c0726e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4cdf48459e6d5777edf473ac78edc32", "guid": "bfdfe7dc352907fc980b868725387e98ad13187ff302d4dbe4d14eed353021a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f3f62f26613c3d2bbace8086c6ef53", "guid": "bfdfe7dc352907fc980b868725387e982f6e097e1758b180440107cc47182612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984787e2296719c57d5597a6589d740792", "guid": "bfdfe7dc352907fc980b868725387e9847fe684d020871324b149bd57a3ea879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5bb705991cd2c77295c8bbde272348", "guid": "bfdfe7dc352907fc980b868725387e98814df151d450ed04febbe8fbd2690346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f19f13c74c1ebb61426d0a3c3984cee", "guid": "bfdfe7dc352907fc980b868725387e98d39161930ba7d06a895b3bd106098d4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db851cb048d56da4a23d72b68572f26a", "guid": "bfdfe7dc352907fc980b868725387e98c04416588bd1bae5ca7f1020fcdd0654"}], "guid": "bfdfe7dc352907fc980b868725387e98f740c6d39d0f9e7651663ce90fc1bf2c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a3d2ec87b36afb93e5cc2b03af3b5038", "guid": "bfdfe7dc352907fc980b868725387e98f065068eb4750271371dc0f82249bfb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989caece53cf47d82781c1f4d0aff81848", "guid": "bfdfe7dc352907fc980b868725387e9803377813f50c7733047c5d4746e8bfff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae4ebd9249a5440861dc56ea41bb498d", "guid": "bfdfe7dc352907fc980b868725387e9863d6e4b6eb4608c9318156b6d33689e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eea3a5c08bc6f4b3fe6ae5a3d0f9c1f", "guid": "bfdfe7dc352907fc980b868725387e98dc5586893a1eb8bc6030d74cc152cd8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9a2b664b336c4c19696c74835e444c", "guid": "bfdfe7dc352907fc980b868725387e984c1eaa2aa562d74ff3824fa8ef1cc01e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecd4a3ce0dbd1b4fce839d669a6486c6", "guid": "bfdfe7dc352907fc980b868725387e980555fccbf54cdfee169969c0b35bf6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be17739d190fd8bb8a34972735f1db77", "guid": "bfdfe7dc352907fc980b868725387e9878342bb130cca37268fd541d0fc0b156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad4d2618dff4cfc89333769177f5645", "guid": "bfdfe7dc352907fc980b868725387e985f9764b789975592a8309833ca685f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c230e802297f50bab186be0fd737bc57", "guid": "bfdfe7dc352907fc980b868725387e98446facd95dbdee0cc3262cf17d551b98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4a23e495db33003901ccf29d5d1c709", "guid": "bfdfe7dc352907fc980b868725387e98cd3a075c9025a61be471cf18a9ad91dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98414e61c98c88aeb94ebfbab098da4420", "guid": "bfdfe7dc352907fc980b868725387e98789ef822ae7b40eb1f31a9414794c727"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229d0d8e575e76980eb5192e6c127df1", "guid": "bfdfe7dc352907fc980b868725387e98a28edb228392af952faef11f95290175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa15edb408dc3c0c14ca77a9ff5d44a", "guid": "bfdfe7dc352907fc980b868725387e98e5309608a7e1281732e43fbfc552a601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbe988e3435fa89bf35ef61aa7e8c6a9", "guid": "bfdfe7dc352907fc980b868725387e9801146f6cb6aa41852dddc0b8089919e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98175fbf9e763fd307d1da1ca25b4d10ae", "guid": "bfdfe7dc352907fc980b868725387e98530dd1257fc321415fd992c0c7eedc3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ca4c7d2b74f7099cac2977c860229a8", "guid": "bfdfe7dc352907fc980b868725387e98c9760cd53fbcb2a5065237556f8599c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff830efa9fb754fe8e93322e40ed0b1c", "guid": "bfdfe7dc352907fc980b868725387e9812f2382ab06a34eab453f1fa48f021f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b3ae993fdbedeb61615902288a723e", "guid": "bfdfe7dc352907fc980b868725387e98ea04fef1d1a54421042f3282f25e1a68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152472a61883ace6ac29821a82e1bd3c", "guid": "bfdfe7dc352907fc980b868725387e9813002c8d1b825f812bf94398232367c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32da19454f95b593b89aa353175edf4", "guid": "bfdfe7dc352907fc980b868725387e98aea4c5223e883b536fce1331590e0c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f005a85895b2ba0950a8f80392584ed", "guid": "bfdfe7dc352907fc980b868725387e98e0b0e5495e407f05b9b8c51950ab16da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98454c045af374d364b2765cd782af4658", "guid": "bfdfe7dc352907fc980b868725387e98819a8fdbbdf52f556d9b41dfae061a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1b69af8bbe887147586f7f185248c4", "guid": "bfdfe7dc352907fc980b868725387e98b654a6743a9c9c3e4d58af69a5c85670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbc0bbdc86f6608f820131f2b59034dc", "guid": "bfdfe7dc352907fc980b868725387e984629b5164bc6951b730b71258caf8455"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830071508426ef8259eef57154a3d3d6", "guid": "bfdfe7dc352907fc980b868725387e986fe53721573a2e6b41124925387c190c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f3e927e6936ab32c123d88e3cb23604", "guid": "bfdfe7dc352907fc980b868725387e985b2b9d12665b79c72e43724ff94675ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984038e381bade1c7a9b0bb1083ce4f120", "guid": "bfdfe7dc352907fc980b868725387e989fe2067eef960d05cb5a6a9d26edfef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e5cdbfc47c3b81c3435a9f1d2610da", "guid": "bfdfe7dc352907fc980b868725387e9829a83582e658b675415bb2fdffe92096"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986041ae36623afaef0268f45cbdfafb8e", "guid": "bfdfe7dc352907fc980b868725387e98e287778aaa73eaa29ba714a63fbebdf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf446f24fe89ac6efab58816b20e82e6", "guid": "bfdfe7dc352907fc980b868725387e98b891aa162b02a1dbce33c60b723643fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e9e71d7ac970fa09d3e54aeaf2964e", "guid": "bfdfe7dc352907fc980b868725387e98dbcc7e75684c99988bc49ec59078af96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870db58954779f2dee1c50a08778bbbb9", "guid": "bfdfe7dc352907fc980b868725387e9828ac38e9e41024b619659f6434ef9af4"}], "guid": "bfdfe7dc352907fc980b868725387e98b2c65aa7a27bc1fc65e31d34c0e77f61", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e98e39a4c6e231bf433e095ec8ef3fea66a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865700059f48bf1e775cb5da2bca8c8b5", "guid": "bfdfe7dc352907fc980b868725387e98d57c66251a7973888a6d71d4a9be4866"}], "guid": "bfdfe7dc352907fc980b868725387e98ab3322c90b1e927516d5a22579f423b6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985f2a43acf9a98c21b0b32b3850287185", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9865871c6c9226ceb8e85144701321cba9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}