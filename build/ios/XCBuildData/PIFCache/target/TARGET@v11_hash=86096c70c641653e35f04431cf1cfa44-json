{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b059e7dbce24129544e223f78049e20", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9836eb6f7e3befa3f51b0725c60b8b65f8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809b3737fd064c2c00a1af241266c38cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857ea356f72848ad9dd1d7886ae89b8b8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809b3737fd064c2c00a1af241266c38cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef39f4071a1f080a58cb0eb4ee4715f2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a13f42e8fd2ff48e2dc73e3128309bea", "guid": "bfdfe7dc352907fc980b868725387e989029f16863633fde06fb34668b461fd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4542bc4c5021beaaacd6da374a84ba9", "guid": "bfdfe7dc352907fc980b868725387e98c841e69f9e2343bed754490c46ce5a4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dff2f6e0ccb5853a996eb2a3bf84a216", "guid": "bfdfe7dc352907fc980b868725387e98907712ef287ced7710ce9af07fe3a03f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9898dce07d0728c65367da583b127c2d1a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9814b8643f78dbd5cbbef0494245719024", "guid": "bfdfe7dc352907fc980b868725387e9884ad856d9e9d1d756bf6695aad595204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982494130ef5f8e4289c1837768a86ffb4", "guid": "bfdfe7dc352907fc980b868725387e98dec2f68f2ea70a951f8872dc87c73019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc451669c4013843d76ecf95d48bb179", "guid": "bfdfe7dc352907fc980b868725387e98659d55a8346b36312c99af55b453b827"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1db587004d0d962c373bb9a8034d22", "guid": "bfdfe7dc352907fc980b868725387e98467eccff80b764cbe5aacf879bf2b784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3aedc6bfe100f2f569cbdcae597cf0e", "guid": "bfdfe7dc352907fc980b868725387e98e2bccde5beb4836dd29dc1cc97d2df0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e9fb817c9d891604d7d60dcb554468", "guid": "bfdfe7dc352907fc980b868725387e983b3865fabc1c106be5b76d1ee3e0bdb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d50aaa253f541d0d3b3ad083d35bec7", "guid": "bfdfe7dc352907fc980b868725387e984f633e276446733f9a6773b4d3e5df6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c89d7116df224c21f310eeb53d8395", "guid": "bfdfe7dc352907fc980b868725387e98a90dd8ec4d4907493deed8ce934a042b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816ce54398fe07fee6b673cfe4e2a6d64", "guid": "bfdfe7dc352907fc980b868725387e98cfdb89de0ee9a71ef5722b4bbb45aea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf095c9c33ddaf8574c0a89bf4b3d8f2", "guid": "bfdfe7dc352907fc980b868725387e9822d57a76b941933c846b94d2dd43dcfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa46261ba61441c187a6c3122139e7c", "guid": "bfdfe7dc352907fc980b868725387e989a6bae956c7af65818c002b186b9b560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6fc8e9175dc5dc30e6fc142f72860c6", "guid": "bfdfe7dc352907fc980b868725387e983a34a5d355aba6ddaf75ee0df27863e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981159940a57b4a8d3e8ce1c035aaf9b44", "guid": "bfdfe7dc352907fc980b868725387e982cfd2465585672b1ff1da8fe9430e0e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c644654e5fe48aeac8159eafd633ad", "guid": "bfdfe7dc352907fc980b868725387e98eee0428d23e37f2ab5882fc8ec5740fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aeca8eb140b242dde3a8af580b4c2c0", "guid": "bfdfe7dc352907fc980b868725387e985e5db6150a15f5e6006331d46c10a944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5b7c46f11b28949c0552b9399179a9", "guid": "bfdfe7dc352907fc980b868725387e989693d8f71e75021158a694e8df3db4d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e70f27c5ae1a5ffbfb37c22db1e863", "guid": "bfdfe7dc352907fc980b868725387e986c7e775b9f071dbce546ad0b74f58417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c9ffa0d11cd57d67fa01ee964c00e7e", "guid": "bfdfe7dc352907fc980b868725387e98fc9689422e55b1bc6fe0c0b5058f31c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b31ce701362e3cc82360c46ad7a5127", "guid": "bfdfe7dc352907fc980b868725387e98232b309932a7eec20e480bb6a943e580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceaf72d06ca3b16e615312785c677e11", "guid": "bfdfe7dc352907fc980b868725387e98f6b6142018e70ff68879beeaff945ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61f56d0badbbe229dca9c2dcf5a68f3", "guid": "bfdfe7dc352907fc980b868725387e9880c95430bd654533febb47082782e7c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc70e7ee2192c3018b79a0e84409c64", "guid": "bfdfe7dc352907fc980b868725387e9875d850164924635e1fae331731b54ba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6322b1e615e85f4cbc1e47470bc1bfe", "guid": "bfdfe7dc352907fc980b868725387e98a20f2e1bdf4842ec1d20f9f90d805e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e917acffb369b8196ab1d37928ab99b", "guid": "bfdfe7dc352907fc980b868725387e98d6dbf69eb589f95c121d421b70f01185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac9a09d18c377a53149fd544d2ab108", "guid": "bfdfe7dc352907fc980b868725387e98349847515150dbacac9ae05ebb6f778b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a8ed3ab35f16c73d9a20ac0791f767", "guid": "bfdfe7dc352907fc980b868725387e98c77e03a4ece77cc6ec67eafce5f2af1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491ff8393aab6f890d9fb101364f3727", "guid": "bfdfe7dc352907fc980b868725387e9813478e08af6335cc98bd501c2007b83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cff4dde03e2fa2b6d6e73bdc2f76814b", "guid": "bfdfe7dc352907fc980b868725387e98a8c5b984e21308b0dce728b89334bdb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce91a8aab22d04882cc4d165d4f11fda", "guid": "bfdfe7dc352907fc980b868725387e980144398821435a096666d62e53ca96d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fa2f26932a5de17c17e6b63e4be3d18", "guid": "bfdfe7dc352907fc980b868725387e9887e1c74ad410f7b9868efb59ed6401c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd7e746daab91ea4f0e6152f122d31da", "guid": "bfdfe7dc352907fc980b868725387e9857b0eba5f8ced115900a6e575e5166a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb87cf4d430a6ab7fcdd8792882d788", "guid": "bfdfe7dc352907fc980b868725387e98d5a39b28df8ca17f843ebabc37551c59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db0ad14b40a5683dc24a61f013c45028", "guid": "bfdfe7dc352907fc980b868725387e989a5a93ab63725ff997e93d5c9d3b4c04"}], "guid": "bfdfe7dc352907fc980b868725387e98506c3693b2724c8c2352323373918cbf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e9868c4c0689854101972cc744a0fe7a4be"}], "guid": "bfdfe7dc352907fc980b868725387e9833fe4021362c657e0e04b36febee34e0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9874e6a6bbfb3914a1da229b6bc7b4dece", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}