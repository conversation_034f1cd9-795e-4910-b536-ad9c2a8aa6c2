{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ccca10ffbf80f750242af1fbaa594a87", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9883d8c87ee861bb6dbfc08bb47275ed7a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d717e1d2373a9e595f78c43dad248e9b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed79bb61407ab3961515ebb01ec3563a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d717e1d2373a9e595f78c43dad248e9b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98143267538670f8093e057c7cb044f361", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b1adb0f3c17149ba97a037259fa0d0b2", "guid": "bfdfe7dc352907fc980b868725387e98676387a37a59d3a26ac8cfb4995a23b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb12c4b8cb31d4f2f298b2bda9c01dea", "guid": "bfdfe7dc352907fc980b868725387e98214696058ecf282368768ab66368ac87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837d343716f0d40ffe0e04d017bc35d75", "guid": "bfdfe7dc352907fc980b868725387e98f6ce9ce0ba090f8f281575431d95f8d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985adc842a95de405e4e1bbda5cb2e9aba", "guid": "bfdfe7dc352907fc980b868725387e9881e66ecd8d65a031604199a947857c04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437d57f42907efc4e0325f2793e90a0f", "guid": "bfdfe7dc352907fc980b868725387e9850d84ea9f240e58e3f1ba274d63d9ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0dd0d3b15da8701a0c98c3517888e1c", "guid": "bfdfe7dc352907fc980b868725387e983ea62eca1e8f7c490971eb2b1fabd0ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f63ecb8277bfda8c92c440926f1aabf", "guid": "bfdfe7dc352907fc980b868725387e98feb3044fe4fe478f0fe871c2dc21a03e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98546747a358d5ebc3d373ff90b7e9a126", "guid": "bfdfe7dc352907fc980b868725387e9875789aafbed7ff2c877cc183243af4c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b475cef44dc6ec6f024edb0f14c404", "guid": "bfdfe7dc352907fc980b868725387e98b0564b895f0f048d7eb298a564f96f2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab425c293530fc0ed4ef82578a89c9c", "guid": "bfdfe7dc352907fc980b868725387e9807b65da67dcd2f9d847061ff3d7e7f30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829792cd27ba11749ae0d94759d0aef82", "guid": "bfdfe7dc352907fc980b868725387e981fc9d0a7441dc92b497d2b5492875f1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e869be0a15a2048b915fbc69e6b376", "guid": "bfdfe7dc352907fc980b868725387e98e108b2b97a4c9dbd068aea59e107c902", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243a43a1bfd0b5e2d987d481c99301bb", "guid": "bfdfe7dc352907fc980b868725387e986bd4a82baa858df9cd9c70e9aba0d918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae23cd75f0836fccb3ea20376de7d5e3", "guid": "bfdfe7dc352907fc980b868725387e981bc84c98096f38cb92f745e87c440dbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6ec1061e09c2ff0dbac3eb49c4be08b", "guid": "bfdfe7dc352907fc980b868725387e98a134246105e2e8b886e9dffe9e0796fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c8bf5ad5670e72d23b0004640b85d81", "guid": "bfdfe7dc352907fc980b868725387e9841841cb1abacd16a40d3e9f887e4b324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b398590ae8b9ec52693ea85b3cc1a0a", "guid": "bfdfe7dc352907fc980b868725387e98641f54ed00fc7bddaa4abf2f00fcf2b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa074d70467eae558a11b7a754d29e2f", "guid": "bfdfe7dc352907fc980b868725387e985c8edbba67130af0a2f7903df665b41e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847fd16141fd404145b75771c922bb60f", "guid": "bfdfe7dc352907fc980b868725387e9845552ccdb1a5e3494eda45b071fa1bae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874578b45dd147f5a4336607bd2f16d59", "guid": "bfdfe7dc352907fc980b868725387e9835cca99ca487e1168a00eebf57c29ac4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff69a57ff44c735e0f1cef5c423c4ef9", "guid": "bfdfe7dc352907fc980b868725387e9895afa2c8ed78379636ebbbf97f4ef4a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e22645103c235ede44d57a9a1a2bda3a", "guid": "bfdfe7dc352907fc980b868725387e9880ba015f7ee81b650208925cc2c10edf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a53635f453fab8756d6aa4518b8f3f6", "guid": "bfdfe7dc352907fc980b868725387e9834d3b50193c578976bc98ce404c2519c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94bffb900ee73f2111f61ae1d166ff4", "guid": "bfdfe7dc352907fc980b868725387e983d36d0a0425bfa58b4bb2388c73a573e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50da8f8ccaae51c062bd47d58ec63c3", "guid": "bfdfe7dc352907fc980b868725387e985cb54fb5d45d441fdc74387bff84f7a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a347252e5420ac8990d654b1555f27e5", "guid": "bfdfe7dc352907fc980b868725387e98ad94de1f56019518cbf98c48cffaeca0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1b0c27042c972246eb14ee8cbdbc8c", "guid": "bfdfe7dc352907fc980b868725387e98275384dfb6cd22b6cf119ba9360ea5dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c46d750ad2a93eaf72e503b6300e84d3", "guid": "bfdfe7dc352907fc980b868725387e982a56257537dc534512366a082f4c205f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d6e04d210d08660abcd7b2aef9768e9", "guid": "bfdfe7dc352907fc980b868725387e98fa0c19f940882ffedded3701a0ae7db0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d428a43badd62ea958992c2d876a1ac3", "guid": "bfdfe7dc352907fc980b868725387e98fe7dbb6edb82ef712878473fdac0d1b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcabf43982d33c82cbb3dc40d39935af", "guid": "bfdfe7dc352907fc980b868725387e980a85703fdb30bb41dd6b55c204ed41dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba562f322dd50d2e596db271296cf5b", "guid": "bfdfe7dc352907fc980b868725387e98a3c919d984517130187d4c985d62df89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c066595d4c1256687b1f2a34f063b50e", "guid": "bfdfe7dc352907fc980b868725387e985b7a4f2270c14df7e37a41fd07580b3a"}], "guid": "bfdfe7dc352907fc980b868725387e9897547e0689742bafd0d6c4ac60577a19", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98126d26b84e7f57d94f41af3665177471", "guid": "bfdfe7dc352907fc980b868725387e9849c6999340a26b687db9465c1dcf0e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f4cb0280d37efa0e216f0acd0c9246", "guid": "bfdfe7dc352907fc980b868725387e98bd944acfa46a3858caa9b3336860484b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f15c83a8b29ac9bbcc6a8aed2c01871e", "guid": "bfdfe7dc352907fc980b868725387e98aa691e10c5900890dfb2db7b739f20ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a056d21217c34753892a39b7eaaabf", "guid": "bfdfe7dc352907fc980b868725387e98b5191945307152d8e0d6165cd5ffd7c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4490a1ff479069aca53290514c6c290", "guid": "bfdfe7dc352907fc980b868725387e98bb2bc5602a51effaf754cfe55067a439"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824882757e328f49a0d08ad7387b80046", "guid": "bfdfe7dc352907fc980b868725387e986bd1b4dfafde9bb70bf9fd3f0af137c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f02ead82519c4fd4a46ef3d44556824", "guid": "bfdfe7dc352907fc980b868725387e98754699b6f5e76b8045cb9a1e99ab3d32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c854aa31bd54e17e206e54bb9df65a90", "guid": "bfdfe7dc352907fc980b868725387e980dd13974d701c4c446cd77ed6b678c78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efd9b9a8e9f76e843f6a7a9efd77b961", "guid": "bfdfe7dc352907fc980b868725387e989372c0d541589ed8b9e17f35d31e8243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a80b9b17e5dc2656c0de8459eb6082", "guid": "bfdfe7dc352907fc980b868725387e984ef802fde0559c1dd974308e683f0500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aea3f443a0a12973e9ee714b2d396b84", "guid": "bfdfe7dc352907fc980b868725387e98685833a37202fd0f889ddf633fddbdf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f367bdede77b4232fc19e4857e75b340", "guid": "bfdfe7dc352907fc980b868725387e9800a0c7f43e7b40ad3474bee03b9633e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2fadfc76f0c18c2b083f8e458adb4cd", "guid": "bfdfe7dc352907fc980b868725387e987928e4d2cc4e71106cb51c85caac1d1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a9bc0f3268b34d34e8b0b89cb1dd83e", "guid": "bfdfe7dc352907fc980b868725387e9868b5e1c17b7b50cde586b72bdd974a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fe511c0abf627cd4ef0f6458b1331bc", "guid": "bfdfe7dc352907fc980b868725387e98b01c0b0a8bef5376abf9c0b41ac6a1aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5bb9e2a73a3fd5c8429b9584e256ab5", "guid": "bfdfe7dc352907fc980b868725387e982326c5fe721c1818bafc302c410b4452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881c9b6a855fdaa8293e3670e2b5bf99a", "guid": "bfdfe7dc352907fc980b868725387e98496e729c46dd8f10568721cbaef6f501"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fea372e7cb314ec59bb8cdeaf645b07", "guid": "bfdfe7dc352907fc980b868725387e98450555eea1546cf37be3480db58e81da"}], "guid": "bfdfe7dc352907fc980b868725387e98d9728a048325a6ac9c334a2ff822de1d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e98dc1b3a63fa3b3b09467ef20e0650eceb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e52b751c087231757083d511f5ac87", "guid": "bfdfe7dc352907fc980b868725387e98f98293fdd26ccd5b177e16b49ef3bc25"}], "guid": "bfdfe7dc352907fc980b868725387e98534864e042991fe4f4f6b2513618a72c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987229951e8617edc5637602e6ed935b71", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9853444557ea2a4d3fccfa3c5a91e38076", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}