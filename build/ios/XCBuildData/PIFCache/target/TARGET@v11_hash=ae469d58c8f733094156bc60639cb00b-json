{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98178233dfafef95c4f3109604de377779", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dba6e754e6704d3cb0d341730ccb2950", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a7b09929094f440e9d9bb18e99a6d31", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98844fcec3e8dfcabf1c86d84bfde4b0aa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a7b09929094f440e9d9bb18e99a6d31", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c7ea92d43c66de8f729a49a5691f05a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b381b2c87666ad368a4c77172b14f4ad", "guid": "bfdfe7dc352907fc980b868725387e988ea2ab18f860ea00c53be86419dcffa2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f052d10626553fff7b1126f5000d37", "guid": "bfdfe7dc352907fc980b868725387e9831c7a288902f1072a3c9928177d210f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab8153218dcef5bfa9612b945b3ce057", "guid": "bfdfe7dc352907fc980b868725387e98707020e84171829f97207f1bd1badb78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98933d91f0d37de6c163b575dfb36b7ea7", "guid": "bfdfe7dc352907fc980b868725387e9875b06c2237d3ade12a0949ca8a13f4cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821cf439fb41ae5b49a7ab39c641d57d3", "guid": "bfdfe7dc352907fc980b868725387e980080d5284f7554653220cad53898e2f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6db89d52fa79904bdc7c92beedbab90", "guid": "bfdfe7dc352907fc980b868725387e983214d066e8dbb5ec673d362ba5bf667f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b84eea5141b701923a51e53b177dd27", "guid": "bfdfe7dc352907fc980b868725387e9838687ac1059d7f074e87b3ae8a863a39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d6b9c8e63a1de551fd15a5a57391c8a", "guid": "bfdfe7dc352907fc980b868725387e98d8456d25f60bae78ad9706d79834a2d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98765d208aa376c9446acf12a9320fbea4", "guid": "bfdfe7dc352907fc980b868725387e98b6683fdaa404c1fa1249371ec9183cda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef418aead52b3903a8825f744bd10b2", "guid": "bfdfe7dc352907fc980b868725387e98072f015cc243bebc8d7c9c20a5137d4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159cadba43211368089741d2a5ce7ce3", "guid": "bfdfe7dc352907fc980b868725387e980b496da19d47916eabe1a40ea6c1e405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a05a09bebdfeb7cc44ab22cca0b79c8", "guid": "bfdfe7dc352907fc980b868725387e98c0754b0150e9c68727f956c70dfef07d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652bdaad47aa4c674e9bc7b34a6adaac", "guid": "bfdfe7dc352907fc980b868725387e986a19c78fc7b60ee676d7db2a9038113c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffb8308c5b2fbf362913409d8de9d8e0", "guid": "bfdfe7dc352907fc980b868725387e986563ed6d2764ba5b98ec4172284c6a54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d56238b6f677706827f3e5360020566", "guid": "bfdfe7dc352907fc980b868725387e98ec6331d1c06f1584eb14b4b35ce2e0aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98451e40a8b6ea7c8750aa9bf4eda03bbf", "guid": "bfdfe7dc352907fc980b868725387e986c39066396d8e4a4086f79e1d1dbc8d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809298f3813d286b2cdaac53ecea70845", "guid": "bfdfe7dc352907fc980b868725387e98831dc4e7111b992be5f91386364f1945", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98017be98105d15a60108303ba0e3f993f", "guid": "bfdfe7dc352907fc980b868725387e9899aa8147f2edce1464bcb070d5dd7358", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a846e19c914142bde2a4036cd406dfb", "guid": "bfdfe7dc352907fc980b868725387e98176d4806ce6404da00b9f883e22501d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6880df74fff29472d5e3515aeea52ba", "guid": "bfdfe7dc352907fc980b868725387e98450f42050bcd0a09e9d3e4485fb9955f", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809791bee2c0b476f252fa4238a900fe6", "guid": "bfdfe7dc352907fc980b868725387e980039dfab8f921b45038b48baee23ecf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801859f80de81a69b50d284dad386980e", "guid": "bfdfe7dc352907fc980b868725387e986066a98f03b41051859f705d19a3fe1a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b20024a7a9e08d787e4e36c3b919a5f2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad650dc7eda128a614f3baba0e757614", "guid": "bfdfe7dc352907fc980b868725387e981d2f6de161f5e8f1ba68f3f63ef2e79b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987970b16764405fd9dd1464a492603aee", "guid": "bfdfe7dc352907fc980b868725387e988a0b436f86220ae6408e04a77ddb77b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4fd0190cbe4dd58c527719ffa1303a6", "guid": "bfdfe7dc352907fc980b868725387e98ae8980b5ac3e87aba3bb043dbee63a0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9128ab178469a6213c5cb37bf5096e6", "guid": "bfdfe7dc352907fc980b868725387e988626db813670967adfddc6cff5761e20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891201a344a93b214ed13fc137211ba7f", "guid": "bfdfe7dc352907fc980b868725387e980bf18980c5ada2a06062e04152b151e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983525f8859d9a025eafe1479c778f0c13", "guid": "bfdfe7dc352907fc980b868725387e98ca3a6bec7ddd285f38ca6ddd1e57f088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2f31a88bb176135718d1ed22335327", "guid": "bfdfe7dc352907fc980b868725387e981a97e4973742571442919927ac573e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a9081b349052ed5376ccb74c6b9db21", "guid": "bfdfe7dc352907fc980b868725387e986b3e2813e90fb5cd86685ba558d3da3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec3e412faa66e95fc6e1da43f852354", "guid": "bfdfe7dc352907fc980b868725387e98e5e5dd1fc3b6023a0d16f339aebf8e64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7869adae1bf74387b7a22dcde035db", "guid": "bfdfe7dc352907fc980b868725387e98a0f17bbc6d636a5878554c2055ebca44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b5a0d2fa350458c6dbb7bc643ba15a", "guid": "bfdfe7dc352907fc980b868725387e982efea0b1d574a43d20daa6216777d425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9c84057659f3c4fe08b17b91d6d68bd", "guid": "bfdfe7dc352907fc980b868725387e9816faadacbe3938ee2cd688be577a05da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3fdac8e24d41fea002f8a8c998609f2", "guid": "bfdfe7dc352907fc980b868725387e986ffffc81685b8a5f81037801f08b6df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895fe2ac6555c429103181204e82d7025", "guid": "bfdfe7dc352907fc980b868725387e98a29b029f114e87baaee41eed9673ef82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb49328e2f39382de391a72164563986", "guid": "bfdfe7dc352907fc980b868725387e9824d514492990a5e0e00b8429cc02a257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98991cb8659bb74802585648bee0e7bbce", "guid": "bfdfe7dc352907fc980b868725387e98e15179d4e56521211d3e4fb5adaca9e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc79e12880ab142b5de398c4822fd2c", "guid": "bfdfe7dc352907fc980b868725387e98be4f31f69419df5bcffc947168aa2e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d68a23a9601b618f9b1dbcd7e4b531b6", "guid": "bfdfe7dc352907fc980b868725387e98352d6b8e13eb20cd79f11849e5e3e113"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce4b41f746673f2fd98a72fded83bd33", "guid": "bfdfe7dc352907fc980b868725387e9815efdeca87d578d1d966bb5c8b325283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b0f4e3c90fe9b83d3546fc23fd4a82d", "guid": "bfdfe7dc352907fc980b868725387e9885b7962efd7cd62d37cbfa8cfb91e35f"}], "guid": "bfdfe7dc352907fc980b868725387e981707142e7b726c38cdc5ba73a4049bff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e984fc53a5e88e372225419daa4832f1403"}], "guid": "bfdfe7dc352907fc980b868725387e98c52adf1a106d5fc7b919616b9f57ff33", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f8a52cddabba1b06165dc7d6df0e8b67", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e989a0d4c65d65fc1a7accc04e232951bfd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}