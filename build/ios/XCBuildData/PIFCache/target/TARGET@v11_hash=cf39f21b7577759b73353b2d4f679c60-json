{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886a03a26b90e4cfcfc17755baf60383c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a20d553b3b4fa171bc3a409797f532d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd13025388af63bc2beadc52602de12d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980a2382b3a0190d927fadd2dcbdef01be", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd13025388af63bc2beadc52602de12d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989941b189480980a62b8dbe197a9a876e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989ef1c616d877fe2cd88c7d1ea890e9e4", "guid": "bfdfe7dc352907fc980b868725387e989905fabf46d0fa6abfb5e4056732170e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b3ce87d39a7b063e501e659dcd4079a6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987c88278054aaf9079f58fc7b1fc758ae", "guid": "bfdfe7dc352907fc980b868725387e98b788898e940a04cfc1b85ffd22f4b529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c547c0e6e2ea3240c500d6df92e1e10", "guid": "bfdfe7dc352907fc980b868725387e985bc77f206d37568c146057f9f4c2f8fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981857b74684999423c94766de5a32dcbd", "guid": "bfdfe7dc352907fc980b868725387e98e283b2be351afbbccd1112e4d850d236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859638d19ab8db5934903d0d58a250fd1", "guid": "bfdfe7dc352907fc980b868725387e987150ace9900dab07518560805fcb8324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bb27cc5723da4b39f5aa70bedb0eabf", "guid": "bfdfe7dc352907fc980b868725387e98c6848b15825c9de198f02f4e5c172e77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0ef1d4d68318740b24ba2ae768db6f3", "guid": "bfdfe7dc352907fc980b868725387e985f1d4b80142842449d05e2e92e1b3bd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cdaea2f44512bd7d490e37ae8815311", "guid": "bfdfe7dc352907fc980b868725387e988c1b41f20c6d2fa89f8db7499576ca67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc5b27be7de855c21037714da2c32191", "guid": "bfdfe7dc352907fc980b868725387e98717871f86cb4232ba1eb1bf850b7dbc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eb361b14c59883df8c8f9a002abaa81", "guid": "bfdfe7dc352907fc980b868725387e98d4603333908cf1f9c948a7ebb06a2e1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981637d946d2acb83ed739235851c4e93c", "guid": "bfdfe7dc352907fc980b868725387e98522067782f0322e2fbaf6581e3146fbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985206deacd1f9928992a4ae9ff91a4a4d", "guid": "bfdfe7dc352907fc980b868725387e98394cce41dffcb242b24863679295097e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d902cca8573ea441d041a901afd03231", "guid": "bfdfe7dc352907fc980b868725387e9869c5fbab7bf21cac88d3bf33d6b0a5fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6d1884ae7b321c18e4d1be584ccbda4", "guid": "bfdfe7dc352907fc980b868725387e9867e37f69b8b79ef90197e766e5e0ce9e"}], "guid": "bfdfe7dc352907fc980b868725387e9815edff8f22814567663b08949596cd37", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9f9b40199f3381bb101a110b3efeef", "guid": "bfdfe7dc352907fc980b868725387e98de88277992e70351a535350cb8273676"}], "guid": "bfdfe7dc352907fc980b868725387e981d14015858392e8d310ceb2ec73b69b5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983cf9603f21535c9c667e6ddf948f9414", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a8e0ed4588472b12e73ff3a26f4cc4a1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}